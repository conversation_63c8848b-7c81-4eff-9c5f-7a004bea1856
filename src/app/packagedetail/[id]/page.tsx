'use client';
import React, { useEffect, useState } from 'react';
// import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ChevronDown, ChevronUp } from 'lucide-react';
// import Link from "next/link";
import Image from 'next/image';
import { Progress } from '@/components/ui/progress';
// import { useRouter } from "next/navigation";
import { useParams } from 'next/navigation';
import { api } from "@/trpc/react";
import { useTranslation } from '@/contexts/LanguageContext';


export default function PackageDetailScreen() {
    const { t } = useTranslation();
    const [isButtonLoading, setIsButtonLoading] = useState(false);
    const [showAllCoupons, setShowAllCoupons] = useState(false);
    // const { showError } = useErrorModal();


    const params = useParams();
    const packageId = params.id;

    const { data, isLoading, error } = api.package.getPacakgeDetail.useQuery({ packageId: packageId as string });

    const packageDetail = data?.data;
    const newAccessToken = data?.newAccessToken;


    useEffect(() => {
        const setToken = async () => {
            if (newAccessToken) {
                try {
                    const res = await fetch('/api/set-token', {
                        method: 'POST',
                        credentials: 'include',
                        body: JSON.stringify({ token: newAccessToken }),
                    });
                    const data = await res.json() as { message: string };
                    console.log('[Set Token Success]', data);
                } catch (err) {
                    console.error('[Set Token Error]', err);
                }
            }
        };
        void setToken();

        if (packageId && packageDetail) {
            localStorage.setItem('packageId', String(packageId));
            localStorage.setItem('price', String(packageDetail.discountPrice));
            localStorage.setItem('title', packageDetail.packageTitle);
        }
    }, [newAccessToken, packageId, packageDetail]);



    const formatNumber = (num: number) => num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = `${date.getMonth() + 1}`.padStart(2, "0");
        const day = `${date.getDate()}`.padStart(2, "0");
        return `${day}/${month}/${year}`;
    };


    const goBackClick = () => {
        window.location.assign("/packagelist");
    };

    const goToCouponDetail = (shopProductID: number) => {
        window.location.assign(`/coupondetail/${shopProductID}`);
    };

    const goToCheckout = () => {
        setIsButtonLoading(true);
        window.location.assign("/checkout");
    };

    const handleGoBackToLineLiff = () => {
        window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
    };

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
                <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
                    <div className="flex items-center justify-center mb-10">
                        <Image src="/images/package/mascot-sad.svg" width={100} height={100} alt="Loading animation"></Image>
                    </div>

                    <div className="text-center space-y-4 mb-8">
                        <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>

                        <div className="space-y-1">
                            <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
                            {/* <p className="text-gray-400">please try again</p> */}
                        </div>
                    </div>
                </div>

                <div className="w-full max-w-md space-y-4 mb-8">
                    <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={handleGoBackToLineLiff}>
                        {t('back_to_home')}
                    </Button>
                </div>
            </div>
        )
    }

    if (isLoading) {
        return (
            <div className="animate-pulse">
                {/* Back button skeleton */}
                <div className="absolute p-2 top-4 left-4 bg-gray-300 rounded-full w-10 h-10 z-20"></div>

                {/* Image skeleton */}
                <div className="relative">
                    <div className="w-full h-48 bg-gray-300"></div>
                </div>

                {/* Progress bar skeleton */}
                <div className="relative py-4 px-6 bg-gray-200">
                    <div className="flex items-center gap-2 relative">
                        <div className="flex justify-between items-center w-full">
                            <div className="h-4 w-28 bg-gray-300 rounded"></div>
                            <div className="relative w-full ml-2">
                                <div className="h-3 bg-gray-300 w-full rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className='p-4'>
                    {/* Title and description skeleton */}
                    <div className="pb-2">
                        <div className="h-7 w-3/4 bg-gray-300 rounded mt-3 mb-3"></div>
                        <div className="h-4 w-full bg-gray-200 rounded mt-3"></div>
                        <div className="h-4 w-5/6 bg-gray-200 rounded mt-2"></div>
                    </div>

                    {/* Price and details skeleton */}
                    <div className="space-y-4 mb-50">
                        <div className="flex flex-col gap-2 mt-4 mb-4">
                            <div className="flex items-center space-x-2">
                                <div className="h-8 w-32 bg-gray-300 rounded"></div>
                                <div className="h-4 w-24 bg-gray-200 rounded"></div>
                            </div>
                            <div className="flex items-center gap-2 mt-2">
                                <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                                <div className="h-4 w-48 bg-gray-200 rounded"></div>
                            </div>
                            <div className="flex items-center gap-2 mt-2">
                                <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                                <div className="h-4 w-36 bg-gray-200 rounded"></div>
                            </div>
                        </div>

                        {/* Coupons skeleton */}
                        <div className="bg-gray-100 rounded-lg p-2">
                            <div className="h-6 w-24 bg-gray-300 rounded mb-3"></div>
                            <div className="flex gap-4 bg-white p-3 mb-2 rounded-lg border border-gray-200">
                                <div className="w-24 h-24 bg-gray-300 rounded-md"></div>
                                <div className="flex-1">
                                    <div className="flex justify-between items-start">
                                        <div className="h-5 w-32 bg-gray-300 rounded"></div>
                                        <div className="h-6 w-16 bg-gray-300 rounded-xl"></div>
                                    </div>
                                    <div className="h-4 w-full bg-gray-200 rounded mt-1"></div>
                                    <div className="h-4 w-28 bg-gray-200 rounded mt-3"></div>
                                </div>
                            </div>
                        </div>

                        {/* Terms skeleton */}
                        <div>
                            <div className="h-6 w-48 bg-gray-300 rounded mb-2"></div>
                            <div className="h-4 w-full bg-gray-200 rounded"></div>
                            <div className="h-4 w-5/6 bg-gray-200 rounded mt-1"></div>
                            <div className="h-4 w-4/6 bg-gray-200 rounded mt-1"></div>
                        </div>
                    </div>

                    {/* Footer skeleton */}
                    <div className="fixed bottom-0 left-0 w-full bg-gray-50 p-4 flex justify-between items-center">
                        <div>
                            <div className="h-4 w-12 bg-gray-200 rounded"></div>
                            <div className="h-8 w-28 bg-gray-300 rounded mt-1"></div>
                        </div>
                        <div className="h-14 w-32 bg-gray-300 rounded"></div>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="">
            {/* {(isLoading || !dataJson) && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
                    <div className="rounded-full">
                        <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation"></Image>
                    </div>
                </div>
            )} */}

            <div className="absolute p-2 top-4 left-4 bg-gray-500 rounded-full text-white z-20">
                <div onClick={goBackClick}>
                    <ArrowLeft className="h-6 w-6 " />
                </div>

            </div>

            <div className="relative">
                {
                    packageDetail?.packageImage ?
                        <>
                            <img
                                src={packageDetail.packageImage}
                                alt="Steak meal"
                                className="w-full h-40 object-fill " />

                            <div className="absolute bottom-0 left-0">
                                <img src="/images/package/coupon-icon.svg" alt="Coupon icon" className="w-16 h-16" />
                            </div>
                            {packageDetail.soldPercentage === 100 && (
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="bg-black bg-opacity-70 text-white text-lg px-6 py-2 font-medium rounded-[20px]">
                                        {t('sold_out')}
                                    </div>
                                </div>
                            )}
                        </>
                        :
                        <div className="w-full h-40 bg-gray-300 flex items-center justify-center rounded-lg">
                            <span className="text-gray-600">{t('no_image')}</span>
                            {packageDetail?.soldPercentage === 100 && (
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="bg-black bg-opacity-70 text-white text-lg px-6 py-2 font-medium rounded-[20px]">
                                        {t('sold_out')}
                                    </div>
                                </div>
                            )}
                        </div>
                }
            </div>

            {packageDetail?.isShowProgressBar && (
                <div className={`relative py-4 px-6 ${packageDetail.soldPercentage === 100 ? 'bg-gray-100' : 'bg-red-100'}`}>
                    <div className="flex items-center gap-2 relative">
                        <div className="flex justify-between items-center w-full">
                            {/* <span className="text-left font-medium text-sm text-red-700 w-35">Limited Offer!</span> */}
                            <span className={`text-left font-medium text-sm ${packageDetail.soldPercentage === 100 ? 'text-gray-700' : 'text-red-700'} w-35`}>
                                {t('limit_offer')}
                            </span>

                            <div className="relative w-full">
                                {/* <Progress value={packageDetail.soldPercentage} className="h-3 bg-gray-300 w-full " /> */}

                                {packageDetail.soldPercentage === 100 ?
                                    <>
                                        <Progress value={0} className="h-3 bg-gray-300 w-full " />
                                        <Image
                                            className="absolute -top-4 transition-all duration-300"
                                            style={{ left: `calc(${packageDetail.soldPercentage}% - 1.4rem)` }}
                                            src="/images/package/mascot.svg"
                                            width={35}
                                            height={35}
                                            alt="coupon icon"
                                        />
                                    </>
                                    :
                                    <>
                                        <Progress value={packageDetail.soldPercentage} className="h-3 bg-gray-300 w-full " />
                                        <Image
                                            className="absolute -top-3 transition-all duration-300"
                                            style={{ left: `calc(${packageDetail.soldPercentage}% - 1rem + 0.8rem)` }}
                                            src="/images/package/fire-icon.svg"
                                            width={22}
                                            height={22}
                                            alt="fire icon"
                                        />
                                    </>
                                }
                            </div>
                        </div>

                    </div>
                </div>
            )}

            <div className='pl-4 pr-4'>
                <div className="pb-2">
                    <h2 className="text-lg font-semibold mt-4">{packageDetail?.packageTitle}</h2>
                    <p className="text-gray-500 text-sm mt-1" style={{ whiteSpace: 'pre-line' }}>
                        {packageDetail?.packageDetail}
                    </p>
                </div>

                <div className="space-y-4">
                    <div className="flex flex-col gap-2 mt-2 mb-4">
                        <div className="flex items-center space-x-2">
                            <h1 className="text-[22px] font-semibold text-quaternary">{formatNumber(packageDetail?.discountPrice ?? 0)} {t('thb')}</h1>
                            {packageDetail?.discountAmount != 0 && (
                                <span className="text-red-700 line-through text-sm">{formatNumber(packageDetail?.originalPrice ?? 0)} {t('thb')}</span>
                            )}
                        </div>
                        <div className="flex items-center gap-2 text-gray-500 text-sm">
                            <img src="/images/package/clock-icon.svg" alt="Coupon icon" className="w-5 h-5" />
                            <span>{t('available_until')} : {formatDate(packageDetail?.dateActiveEnd ?? '')}</span>
                        </div>

                        <div className="flex items-center gap-2 text-gray-500 text-sm">
                            <img src="/images/package/coupon-icon3.svg" alt="Coupon icon" className="w-5 h-5" />
                            <span>{t('includes_coupon', {totalCoupon: packageDetail?.totalCoupon ?? 0} )}</span>
                        </div>
                    </div>

                    <div className="bg-gray-100 rounded-lg p-2">
                        <h3 className="font-bold text-sm mb-1">{t('consist_of_coupons')}</h3>
                        {packageDetail?.coupon && (
                            <>
                                {packageDetail.coupon.length > 0 && packageDetail.coupon.slice(0, showAllCoupons ? undefined : 5).map((item, index) => (
                                    <div
                                        className="cursor-pointer"
                                        key={item.shopProductID}
                                        onClick={() => goToCouponDetail(item.shopProductID)}
                                    >
                                        <div className="flex gap-4 bg-white p-3 mb-2 rounded-lg border border-gray-200">
                                            {/* <img
                                                src={item.image ?? ""}
                                                alt="Coupon"
                                                className="w-24 h-24 rounded-md object-cover"
                                            /> */}

                                            {item.image ? (
                                                <img
                                                    src={item.image}
                                                    alt="Coupon"
                                                    className="w-34 h-24 rounded-md"
                                                />
                                            ) : (
                                                <div className="w-34 h-24 bg-gray-300 flex items-center justify-center rounded-lg">
                                                    <span className="text-gray-600 text-sm">{t('no_image')}</span>
                                                </div>
                                            )}


                                            <div className="flex-1 flex flex-col w-0">
                                                <div className="flex justify-between items-start">
                                                    <p className="font-bold text-sm text-primary line-clamp-1" >
                                                        {item.title}
                                                    </p>
                                                    <Badge variant="outline" className="bg-[#CC5328] text-white rounded-xl text-xs font-medium">
                                                        X{item.couponAmount}
                                                    </Badge>
                                                </div>

                                                <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                                                    {item.detail}
                                                </p>

                                                <div className="mt-auto">
                                                    <p className="text-xs text-gray-500">
                                                        {item.validAge === 'Unlimit'
                                                            ? t('valid_until')+' '+` ${formatDate(item.validUntil)}`
                                                            : t('valid_for')+` ${item.validAge.includes('D')
                                                                ? item.validAge.replace('D', ' '+t('days'))
                                                                : item.validAge.includes('M')
                                                                    ? item.validAge.replace('M', ' '+t('months'))
                                                                    : item.validAge.replace('Y', ' '+t('years'))
                                                            }`
                                                        }
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))
                                }


                                {packageDetail.coupon.length > 5 && (
                                    <div className="text-right mt-1">
                                        <span
                                            className="text-quaternary font-medium cursor-pointer hover:underline inline-flex items-center"
                                            onClick={() => setShowAllCoupons(!showAllCoupons)}
                                        >
                                            {showAllCoupons ?
                                                <span className="flex items-center">{t('view_less')} <ChevronUp className="h-4 w-4 ml-1" /></span> :
                                                <span className="flex items-center">{t('view_all')} <ChevronDown className="h-4 w-4 ml-1" /></span>
                                            }
                                        </span>
                                    </div>
                                )}
                            </>
                        )}
                    </div>

                    <div>
                        <h3 className="font-bold text-base mb-2">{t('term_and_conditions')}</h3>
                        <p className="text-sm text-gray-400" style={{ whiteSpace: 'pre-line' }}>
                            {packageDetail?.termsAndConditions}
                        </p>
                    </div>
                </div>
                <div className="mt-30"></div>

                <div className="fixed bottom-0 left-0 w-full bg-gray-50 p-4 flex flex-col">
                    <div className="flex justify-between items-center w-full">
                        <div>
                            <p className="text-[10px] text-gray-500">{t('total')}</p>
                            <p className="text-[22px] font-bold">{formatNumber(packageDetail?.discountPrice ?? 0)} {t('thb')}</p>                      
                        </div>

                        {packageDetail?.status === 'Active' ? (
                            <Button
                                className="text-white px-8 py-6"
                                style={{ backgroundColor: '#D1C428' }}
                                onClick={goToCheckout}
                                disabled={isButtonLoading}
                            >
                                {isButtonLoading ? (
                                    <div className="flex items-center justify-center w-20">
                                        <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading" />
                                    </div>
                                ) : (
                                    t('buy_package')
                                )}
                            </Button>
                        ) : (
                            <Button
                                className="text-gray-800 px-8 py-6 bg-gray-400 hover:bg-gray-400"
                                disabled={true}
                            >
                                {(() => {
                                    switch (packageDetail?.status) {
                                        case 'LimitQuota':
                                            return t('sold_out2');
                                        case 'LimitPurchase':
                                            return t('limit_reached');
                                        case 'CouponOutOfStock':
                                            return t('ranout');
                                        default:
                                            return 'Unavailable';
                                    }
                                })()}
                            </Button>
                        )}
                    </div>

                    {packageDetail?.isShowPurchaseRemain && (
                        // <span className="text-gray-500 text-sm mt-1">You can still purchase {packageDetail?.quotaAccountRemain} more time(s)</span>
                        <span className="text-gray-500 text-sm mt-1">{t('you_can_still_purchase', {quotaAccountRemain: packageDetail?.quotaAccountRemain ?? 0})}</span>
                    )}

                    {(() => {   
                        let message = '';
                        switch (packageDetail?.quotaPeriod) {
                        case 'Daily':
                            message = 'quota_reached_tomorrow';
                            break;
                        case 'Weekly':
                            message = 'quota_reached_next_week';
                            break;
                        case 'Monthly':
                            message = 'quota_reached_next_month';
                            break;
                        case 'Yearly':
                            message = 'quota_reached_next_year';
                            break;
                        default:
                            message = '';
                        }

                        return (
                            <span className="text-red-700 text-sm block -ml-2 text-sm mt-1">
                                {t(message)}
                            </span>
                        );
                    })()}
                </div>  
            </div>
        </div>

    );
};